/*
 * Copyright (c) 2012-2013. Sencha Inc.
 */
package com.sencha.tools.compiler.sources.types;

import com.sencha.command.environment.BuildEnvironment;
import com.sencha.command.environment.FrameworkEnvironment;
import com.sencha.exceptions.ExParse;
import com.sencha.logging.SenchaLogManager;
import com.sencha.tools.compiler.CompilerContext;
import com.sencha.tools.compiler.CompilerMessage;
import com.sencha.tools.compiler.ast.AstUtil;
import com.sencha.tools.compiler.ast.js.*;
import com.sencha.tools.compiler.meta.Note;
import com.sencha.tools.compiler.meta.Tag;
import com.sencha.tools.compiler.sources.*;
import com.sencha.util.CollectionUtil;
import com.sencha.util.Converter;
import com.sencha.util.JsonUtil;
import org.slf4j.Logger;

import java.util.*;

import static com.sencha.tools.compiler.ast.AstUtil.resolveName;
import static com.sencha.util.StringUtil.escapeForLogging;

public class ClassDefinition extends BaseTypeElement implements Tag.Visitor {
    private static final Logger _logger = SenchaLogManager.getLogger();

    public Map<String, AutoDependency> getScopeAutoDependencies() {
        return _scopeAutoDependencies;
    }

    public void setScopeAutoDependencies(Map<String, AutoDependency> scopeAutoDependencies) {
        _scopeAutoDependencies = scopeAutoDependencies;
    }

    public enum DefineType {
        ObjectLiteral,
        FunctionCall,
        Factory
    }

    public ClassDefinition (String clsName,  BaseNode config, SourceFileSymbols symbols) {
        this(clsName, config, symbols, null);
    }

    public ClassDefinition (String clsName,  BaseNode config, SourceFileSymbols symbols, String baseCls) {
        super(clsName, config);
        _baseCls = baseCls;
        config.setClassDefinition(this);
        _type = DefineType.ObjectLiteral;
        _symbols = symbols;
        processConfig(config);
    }

    public ClassDefinition removeMixin (ClassDefinition mix) {
        _mixedInClasses.remove(mix);
        mix._mixedInBy.remove(this);
        clearCaches();
        return this;
    }

    public ClassDefinition addMixin (ClassDefinition mix) {
        _mixedInClasses.add(mix);
        mix._mixedInBy.add(this);
        clearCaches();
        return this;
    }

    public ClassDefinition getBaseClass() {
        return _baseClass;
    }

    public void setBaseClass(ClassDefinition baseClass) {
        if (_baseClass != null) {
            _baseClass._extendedBy.remove(this);
        }
        _baseClass = baseClass;
        _baseClass._extendedBy.add(this);
        clearCaches();
    }

    private void clearCaches () {
        _actualConfigs = null;
        _fullMembers = null;
        _fullConfigs = null;
        _actualConfigs = null;
        _underrides = null;
        _allUnderrides = null;
        _allBases = null;
    }

    public void replaceUsages(String name, ClassDefinition replacement) {
        Set<ClassDefinition> subClassesToUpdate = new HashSet<ClassDefinition>();
        for (ClassDefinition def : _extendedBy) {
            if (name.equals(def.getBaseClassName())) {
                subClassesToUpdate.add(def);
            }
        }
        for(ClassDefinition def : subClassesToUpdate) {
            def.setBaseClass(replacement);
        }

        Set<ClassDefinition> mixinsToUpdate = new HashSet<ClassDefinition>();
        for (ClassDefinition def : _mixedInBy) {
            if (def._classMixins.contains(name)) {
                mixinsToUpdate.add(def);
            }
        }
        for (ClassDefinition def : mixinsToUpdate) {
            def.removeMixin(this);
            def.addMixin(replacement);
        }
    }

    public String getDetectedApplicationName() {
        return _detectedApplicationName;
    }

    public void setDetectedApplicationName(String detectedApplicationName) {
        _detectedApplicationName = detectedApplicationName;
    }

    public DefineType getType() {
        return _type;
    }

    public void setType(DefineType type) {
        _type = type;
    }

    public Set<ClassDefinition> getMixedInClasses() {
        return _mixedInClasses;
    }

    public void setMixedInClasses(Set<ClassDefinition> mixedInClasses) {
        _mixedInClasses = mixedInClasses;
    }

    public String getExtendName() {
        return _extendName;
    }

    public void setExtendName(String extendName) {
        _extendName = extendName;
    }

    public String getOverrideName() {
        return _overrideName;
    }

    public void setOverrideName(String overrideName) {
        _overrideName = overrideName;
    }

    public boolean isSingleton() {
        return _isSingleton;
    }

    public void setSingleton(boolean isSingleton) {
        _isSingleton = isSingleton;
    }

    public boolean isOverride() {
        return _isOverride;
    }

    public boolean isAutoOverride() {
        return isOverride() && getSourceFile().getSymbols().getTags().containsKey("overrides");
    }
    
    public void setOverride(boolean isOverride) {
        _isOverride = isOverride;
    }

    private Boolean _requiresAsync;

    private boolean requiresAsync(Set<ClassDefinition> processed) {
        if(_requiresAsync != null) {
            return _requiresAsync;
        }
        if(!processed.contains(this)) {
            processed.add(this);
            try {
                if (hasTag("cmd.optimizer.requires.async")) {
                    return _requiresAsync = true;
                }
                for (ClassDefinition base : getAllBaseClasses()) {
                    if (base.requiresAsync(processed)) {
                        return _requiresAsync = true;
                    }
                }
                if(getOverrideTarget() != null) {
                    if(getOverrideTarget().requiresAsync()) {
                        return _requiresAsync = true;
                    }
                }

                SourceFile myFile = getSourceFile();
                for (Dependency dep : myFile.getSymbols().getScopeSymbols().getDependencies(myFile)) {
                    SourceFile depFile = dep.getTarget();
                    
                    // if one of the dependencies is to another class in this
                    // file, ignore it, as looping this file will either be
                    // handled by the recursive caller of this method (see loop below)
                    // or won't matter
                    // this is needed to support some unit tests that define multiple
                    // classes in a single file (OptimizerCommandTest.java, ReferenceOptimizerTest.java)
                    if(depFile == getSourceFile()) {
                        continue;
                    }

                    // if there is a requirement dependency on a class that requires async
                    // then this class must also be left async
                    if (dep.getType() == Dependency.Type.Requirement) {
                        for (Reference ref : dep.getReferences()) {
                            if (ref.getReferenceType().isHardRequirement()) {
                                if (depFile.getSymbols().isOverride()) {
                                    continue;
                                }
                                for (ClassDefinition def : depFile.getSymbols().getClasses()) {
                                    if (def.requiresAsync(processed)) {
                                        return _requiresAsync = true;
                                    }
                                }
                            }
                        }
                    }
                }
                return _requiresAsync = false;
            } finally {
                processed.remove(this);
            }
        }
        if(_requiresAsync == null) {
            _requiresAsync = true;
        }
        return _requiresAsync;
    }
    
    /**
     * indicates that this class requires async loading, so the 
     * define rewriter cannot be used
     */
    public boolean requiresAsync() {
        if(_requiresAsync == null) {
            _requiresAsync = requiresAsync(new HashSet<ClassDefinition>());
        }
        return _requiresAsync;
    }

    /**
     * indicates whether the mixins on this classs are defined in array
     * or keyed object form
     */
    public boolean isNamedMixins() {
        return _namedMixins;
    }

    public void setNamedMixins(boolean namedMixins) {
        _namedMixins = namedMixins;
    }

    private static final String PATH_SEGMENT_OVERRIDES = "overrides";
    private static final String PATH_SEGMENT_LOCALE = "locale";

    public boolean isSingletonOverride() {
        if(!isOverride()) {
            return false;
        }
        ClassDefinition target = getOverrideTarget();
        if(target == null) {
            // If the requested override belongs to the locale package but the target
            // doesn't exist, mute the compiler error.
            final String canonicalPath = getSourceFile().getCanonicalPath();
            if (canonicalPath != null
                && !(canonicalPath.contains(PATH_SEGMENT_OVERRIDES)
                && canonicalPath.contains(PATH_SEGMENT_LOCALE)))
            {
                CompilerMessage.NoOverrideTarget.log(_overrideNode);
            }
            return false;
        }    
        return target.isSingleton();
    }

    public String getClassName() {
        return getName();
    }
    
    public String getNamespace() {
        return getNamespace(getClassName());
    }
    
    public String getShortName() {
        return getShortName(getClassName());
    }
    
    public Map<String, Boolean> getIgnoredNamespaces() {
        return _ignoredNamespaces;
    }
    
    public void ignoreNamespace(String namespace) {
        _ignoredNamespaces.put(namespace, true);
    }
    
    public ObjectLiteral getConfigObj() {
        return _configLiteral;
    }
    
    public String getProcessedConfig() {
        return _processedConfig;
    }
    
    public boolean isFunctionConfig () {
        return _isFunctionConfig;
    }
    
    public SourceFile getSourceFile() {
        return _symbols.getSourceFile();
    }
    
    public void setVirtual(boolean isVirtual) {
        _isVirtual = isVirtual;
    }

    public boolean isVirtual() {
        return _isVirtual;
    }

    public String getBaseClassName() {
        if (_extendName != null) {
            return _extendName;
        } else {
            return "Ext.Base";
        }
    }
    
    public static String getNamespace(String className) {
        String[] packages = className.split("\\.");
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < (packages.length - 1); i++) {
            if (sb.length() > 0) {
                sb.append(".");
            }
            sb.append(packages[i]);
        }
        return sb.toString();
    }
    
    public static String getShortName(String fullName) {
        String[] packages = fullName.split("\\.");
        
        return packages[packages.length - 1];
    }
    
    protected void processObjLiteral(ObjectLiteral obj) {
        _configLiteral = obj;
        
        if (_baseCls != null) {
            _extendName = _baseCls;
            addReference(_baseCls, ReferenceType.ClassExtend, null);
            _classRequires.add(_baseCls);
        }
            
        for (ObjectProperty prop : obj.getElements()) {
            BaseNode nameNode = prop.getLeft();
            _currentPropertyName = AstUtil.resolveName(nameNode);

            if ("extend".equals(_currentPropertyName)) {
                _extendName = AstUtil.resolveName(prop.getRight());
                addReference(_extendName, ReferenceType.ClassExtend, prop.getRight());
                _classRequires.add(_extendName);
            } else if ("mixins".equals(_currentPropertyName)) {
                initMixins(prop);
            } else if ("requires".equals(_currentPropertyName)) {
                initRequires(prop);
            } else if ("uses".equals(_currentPropertyName)) {
                initUses(prop);
            } else if ("override".equals(_currentPropertyName)) {
                _overrideNode = prop.getRight();
                _overrideName = AstUtil.cast(_overrideNode, StringLiteral.class).getValue();
                _isOverride = true;
            } else if ("alternateClassName".equals(_currentPropertyName)) {
                initAlternates(prop);
            } else if ("alias".equals(_currentPropertyName)) {
                initAliases(prop);
            } else if ("xtype".equals(_currentPropertyName)) {
                initXTypes(prop);
            } else if("privates".equals(_currentPropertyName)) {
                BaseNode right = prop.getRight();
                if(right instanceof ObjectLiteral) {
                    ObjectLiteral configs = (ObjectLiteral)right;
                    for(ObjectProperty p : configs.getElements()) {
                        addMember(p);
                    }
                }            
            } else if ("singleton".equals(_currentPropertyName)) {
                BaseNode node = prop.getRight();
                try {
                    String value = "false";
                    if (node instanceof StringLiteral) {
                        value = ((StringLiteral)node).getValue();
                    } else if (node instanceof KeywordLiteral) {
                        KeywordLiteral lit = (KeywordLiteral) node;
                        if(lit.isBooleanLiteral()) {
                            value = lit.getValue();
                        }
                    }
                    _isSingleton = Converter.convert(value, Boolean.class);
                } catch (Exception e) {
                    throw new ExParse(e, "Unable to parse {0} to boolean",
                        escapeForLogging(AstUtil.toSource(node)));
                }
                
            } else if(_configNames.contains(_currentPropertyName)) {
                BaseNode right = prop.getRight();
                if(right instanceof ObjectLiteral) {
                    ObjectLiteral configs = (ObjectLiteral)right;
                    for(ObjectProperty p : configs.getElements()) {
                        _currentPropertyName = AstUtil.resolveName(p.getLeft());
                        processDirectives(p);
                        BaseNode value = p.getRight();
                        ClassConfig config = new ClassConfig(this, _currentPropertyName, value);
                        _configs.put(_currentPropertyName, config);
                        p.setMember(config);
                    }
                }
            } else {
                addMember(prop);
            }
        }

        _currentPropertyName = null;
        
        Collections.sort(_classAliases);
    }

    private void addMember(ObjectProperty prop) {
        BaseNode right = prop.getRight();
        String name = AstUtil.resolveName(prop.getLeft());
        _currentPropertyName = name;
        ClassMember element;
        if(right instanceof FunctionNode) {
            element = new ClassMethod(this, name, right);
        } else {
            element = new ClassProperty(this, name, right);
        }
        prop.setMember(element);
        _members.put(name, element);
        processDirectives(prop);
    }
    
    private void processDirectives(BaseNode node) {
        List<Comment> comments = node.getComments();
        if (comments != null) {
            for (Comment comment : comments) {
                String text = comment.getValue();
                if (Tag.detect(text)) {
                    Note note = comment.getNote();
                    note.visit(this);
                }
            }
        }
    }
    
    @Override public void onAutoDependency (Tag tag) {
        String json = tag.getType();
        if (json != null) {
            json = "{" + json + "}";

            AutoDependency dep = JsonUtil.fromJson(json, AutoDependency.class);
            dep.setPropertyName(_currentPropertyName);
            dep.setClassName(getClassName());
            if(!_detectedAutoDependencies.containsKey(_currentPropertyName)) {
                _detectedAutoDependencies.put(_currentPropertyName, dep);
            }
        }
    }

    public Map<String, AutoDependency> getFrameworkAutoDependencies() {
        if (_frameworkAutoDependencies == null) {

            SourceFile sf = getSourceFile();
            ClassPathScope scope = sf.getScope();
            CompilerContext ctx = scope.getCompilerContext();
            BuildEnvironment env = null;
            if(ctx != null) {
                env = ctx.getBuildEnvironment();
            }

            if(env != null) {
                env = env.getFrameworkEnvironment();
            }

            if(env != null) {
                _frameworkAutoDependencies =
                    ((FrameworkEnvironment)env).loadAutoDepedencies(getClassName());
            }

            if (_frameworkAutoDependencies == null) {
                _frameworkAutoDependencies = new HashMap();
            }
        }
        return _frameworkAutoDependencies;
    }

    public Map<String, AutoDependency> getDetectedAutoDependencies() {
        return _detectedAutoDependencies;
    }

    private void processConfig (BaseNode config) {
        FunctionNode functionNode;
        ObjectLiteral objLiteral = null;
        if (config instanceof ObjectLiteral) {
            objLiteral = (ObjectLiteral) config;
            processObjLiteral(objLiteral);
        } else {
            _isFunctionConfig = true;
            functionNode = getConfigFunctionNode(config);            
            if (functionNode != null) {
                objLiteral = getFunctionReturnLiteral(functionNode);
                processObjLiteral(objLiteral);
            }
        }
    }
    
    private void addAlias(String alias) {
        if (_classAliasesSet.add(alias)) {
            _classAliases.add(alias);
            
            if (alias.startsWith("widget.")) {
                addXtype(alias.substring(7));
            }
        }
    }

    private void addXtype(String xtype) {
        if (_classXtypesSet.add(xtype)) {
            _xtypes.add(xtype);
            
            addAlias("widget." + xtype);
        }
    }
    
    private void initAliases(ObjectProperty alias) {
        if (alias != null) {
            BaseNode als = alias.getRight();
            String aliasStr;
            if (als instanceof ArrayLiteral) {
                for (BaseNode el : ((ArrayLiteral) als).getElements()) {
                    aliasStr = AstUtil.cast(el, StringLiteral.class).getValue();
                    addAlias(aliasStr);
                }
            } else if(als instanceof StringLiteral) {
                aliasStr = ((StringLiteral) als).getValue();
                addAlias(aliasStr);
            } else {
                CompilerMessage.UnexpectedNodeType.log(als, "Expected String or Array");
            }
        }
    }
    
    private void initAlternates(ObjectProperty alternates) {
        if (alternates != null) {
            BaseNode alt = alternates.getRight();
            if (alt instanceof ArrayLiteral) {
                for (BaseNode el : ((ArrayLiteral) alt).getElements()) {
                    _classAlternates.add(AstUtil.cast(el, StringLiteral.class).getValue());
                }
            } else if(alt instanceof StringLiteral) {
                _classAlternates.add(((StringLiteral) alt).getValue());
            } else {
                CompilerMessage.UnexpectedNodeType.log(alt, "Expected String or Array");
            }
        }
    }
    
    private void initXTypes(ObjectProperty prop) {
        BaseNode node = prop.getRight();
        String xtypeStr;
        if(node instanceof StringLiteral) {
            xtypeStr = ((StringLiteral) node).getValue();
            
            addXtype(xtypeStr);
        } else if(node instanceof ArrayLiteral) {
            ArrayLiteral array = (ArrayLiteral)node;
            for(BaseNode n : array.getElements()) {
                xtypeStr = AstUtil.cast(n, StringLiteral.class).getValue();
                addXtype(xtypeStr);
            }
        } else {
            CompilerMessage.UnexpectedNodeType.log(node, "Expected String or Array");
        }
    }

    public String getOverride() {
        return _overrideName;
    }

    public ClassDefinition getOverrideTarget() {
        return _overrideTarget;
    }

    public void setOverrideTarget(ClassDefinition target) {
        _overrideTarget = target;
    }

    public String getXType () {
        if(_xtypes.isEmpty()) {
            return null;
        }
        return _xtypes.get(0);
    }
    
    private Reference addReference(
            String name,
            ReferenceType type,
            BaseNode node) {
        SourceFile sf = getSourceFile();
        
        if(sf != null) {
            Reference dr = new Reference(
                name,
                type,
                sf,
                node);

            _symbols.addReference(dr);
            return dr;
        }
        return null;
    }
    
    public void initRequires(ObjectProperty requires) {
        if (requires != null) {
            String reqName;
            BaseNode req = requires.getRight();
            if (req instanceof ArrayLiteral) {
                for (BaseNode el : ((ArrayLiteral) req).getElements()) {
                    if(el instanceof StringLiteral) {
                        reqName = AstUtil.cast(el, StringLiteral.class).getValue();
                        _classRequires.add(reqName);
                        Reference ref = addReference(
                            reqName,
                            ReferenceType.ClassRequire,
                            el);
                        if(ref != null) {
                            ref.setReferenceNode(requires);
                        }
                    }
                }
            } else if(req instanceof StringLiteral) {
                reqName = ((StringLiteral) req).getValue();
                _classRequires.add(reqName);
                Reference ref = addReference(
                    reqName,
                    ReferenceType.ClassRequire,
                    req);
                if(ref != null) {
                    ref.setReferenceNode(requires);
                }
            } else {
                CompilerMessage.UnexpectedNodeType.log(req, "Expected String or Array");
            }
        }
    }

    public void initUses(ObjectProperty uses) {
        if (uses != null) {
            String useName;
            BaseNode useCls = uses.getRight();
            if (useCls instanceof ArrayLiteral) {
                for (BaseNode el : ((ArrayLiteral) useCls).getElements()) {
                    useName = AstUtil.cast(el, StringLiteral.class).getValue();
                    _classUses.add(useName);
                    Reference ref = addReference(
                        useName,
                        ReferenceType.ClassUses,
                        el);
                    if(ref != null) {
                        ref.setReferenceNode(uses);
                    }
                }
            } else if(useCls instanceof StringLiteral) {
                useName = ((StringLiteral) useCls).getValue();
                _classUses.add(useName);
                Reference ref = addReference(
                    useName,
                    ReferenceType.ClassUses,
                    useCls);
                if(ref != null) {
                    ref.setReferenceNode(uses);
                }
            } else {
                CompilerMessage.UnexpectedNodeType.log(useCls, "Expected String or Array");
            }
        }
    }

    public void initMixins(ObjectProperty mixins) {
        if (mixins != null) {
            BaseNode mixin = mixins.getRight();
            if (mixin instanceof ArrayLiteral) {
                for (BaseNode el : ((ArrayLiteral) mixin).getElements()) {
                    String mname = null;
                    if(el instanceof StringLiteral) {
                        mname = ((StringLiteral) el).getValue();
                    } else if(el instanceof PropertyGet) {
                        mname = resolveName(el);
                    }
                    if(mname != null) {
                        _classMixins.add(mname);
                        addReference(
                            mname,
                            ReferenceType.ClassMixin,
                            el);
                    }
                }
            } else if(mixin instanceof ObjectLiteral) {
                _namedMixins = true;
                for (ObjectProperty el : ((ObjectLiteral) mixin).getElements()) {
                    String mname = AstUtil.resolveName(el.getRight());
                    _classMixins.add(mname);
                    addReference(
                        mname,
                        ReferenceType.ClassMixin,
                        el.getRight());
                }
            } else {
                CompilerMessage.UnexpectedNodeType.log(mixin, "Expected Array or Object");
            }
        }
    }

    public List<String> getAlternateClassNames() {
        return _classAlternates;
    }

    public List<String> getAliases() {
        return _classAliases;
    }

    public Set<String> getMixins() {
        return _classMixins;
    }
    
    public List<String> getRequires() {
        return _classRequires;
    }
    
    public List<String> getUses() {
        return _classUses;
    }
    
    public List<String> getShallowXTypes() {
        List<String> xtypes = new ArrayList<String>();
        
        if (_xtypes != null) {
            xtypes.addAll(getLocalXTypes());
        }
        
        return xtypes;
    }
    
    public List<String> getAllXTypes() {
        Set<String> allXtypes = new LinkedHashSet<String>();

        getAllXTypes(allXtypes);

        List<String> ret = new ArrayList<String>(allXtypes);
        
        return ret;
    }

    public List<String> getLocalXTypes() {
        Set<String> localXtypes = new HashSet<String>();

        if (_xtypes != null) {
            localXtypes.addAll(_xtypes);
        }

        return new ArrayList<String>(localXtypes);
    }

    public List<String> getAllAliases() {
        List<String> xtypes = getAllXTypes(),
                     aliases = getAliases(),
                     all = new ArrayList<String>();

        for(String xtype : xtypes) {
            if(!xtype.startsWith("widget.")) {
                all.add("widget." + xtype);
            } else {
                all.add(xtype);
            }
        }

        for(String als : aliases) {
            all.add(als);
        }

        return all;
    }

    public List<String> getLocalAliases() {
        List<String> xtypes = getLocalXTypes(),
            aliases = getAliases(),
            all = new ArrayList<String>();

        for(String xtype : xtypes) {
            if(!xtype.startsWith("widget.")) {
                all.add("widget." + xtype);
            } else {
                all.add(xtype);
            }
        }

        for(String als : aliases) {
            all.add(als);
        }

        return all;
    }
    
    private void getAllXTypes (Set<String> xtypes) {
        if (getBaseClass() != null) {
            getBaseClass().getAllXTypes(xtypes);
        }

        for (ClassDefinition def : _mixedInClasses) {
            def.getAllXTypes(xtypes);
        }

        if (_xtypes != null) {
            xtypes.addAll(_xtypes);
        }

    }
    
    public boolean isInstanceOf(String className) {
        if(getClassName().equals(className)) {
            return true;
        }
        for(ClassDefinition base : getAllBaseClasses()) {
            if(base.isInstanceOf(className)) {
                return true;
            }
        }
        return false;
    }
    
    //-------------------------------------------------------------------------

    public static BaseNode setFunctionReturnLiteral (FunctionNode func, BaseNode node) {
        BaseNode last = ((Block)func.getBody()).getElements().get(((Block) func.getBody()).getElements().size() -1);
        boolean hasRetLiteral = false;
        ReturnStatement retStatement = null;
        
        if (last instanceof ReturnStatement) {
            hasRetLiteral = true;
            retStatement = (ReturnStatement) last;
        }
        
        if (!hasRetLiteral) {
            return null;
        }
        
        if(retStatement != null) {
            retStatement.setReturnValue(node);
        }
        
        return func;
    }
    
    public static FunctionNode getConfigFunctionNode(BaseNode config) {
        BaseNode target, expr;
        FunctionCall fcall;
        FunctionNode ret = null;
        ParenthesizedExpression parent;
        
        if (config instanceof ParenthesizedExpression) {
            parent = (ParenthesizedExpression) config;
            expr = parent.getExpression();
            if (expr instanceof FunctionCall) {
                fcall = (FunctionCall) expr;
                target = fcall.getTarget();
                if (target instanceof FunctionNode) {
                    ret = (FunctionNode) target;
                }
            }
        } else if (config instanceof FunctionCall) {
            fcall = (FunctionCall) config;
            target = fcall.getTarget();
            if (target instanceof FunctionNode) {
                ret = (FunctionNode) target;
            } else if (target instanceof ParenthesizedExpression) {
                parent = (ParenthesizedExpression) target;
                expr = parent.getExpression();
                if (expr instanceof FunctionNode) {
                    ret = (FunctionNode) expr;
                }
            }
        } else if (config instanceof FunctionNode) {
            ret = (FunctionNode) config;
        }
        
        return ret;
    }
    
    //-------------------------------------------------------------------------

    public static ObjectLiteral getFunctionReturnLiteral(FunctionNode func) {
        BaseNode last = ((Block)func.getBody()).getElements().get(((Block) func.getBody()).getElements().size() -1), val;
        ReturnStatement ret;

        if (last instanceof ReturnStatement) {
            ret = (ReturnStatement) last;
            val = ret.getReturnValue();
            if (val instanceof ObjectLiteral) {
                return (ObjectLiteral) val;
            }
        }
        
        return null;
    }
    
    public ClassMember getLocalMember(String name) {
        ClassMember member = _overrides.get(name);
        if(member == null) {
            member = _members.get(name);
        }
        return member;
    }
    
    public ClassMember getMember(String name) {
        ClassMember member = getLocalMember(name);
        if(member == null && getBaseClass() != null) {
            member = getBaseClass().getMember(name);
        }
        if(member == null && _overrideName != null) {
            member = getOverrideTarget().getMember(name);
        }
        return member;
    }
    
    public Map<String, ClassMember> getLocalMembers() {
        Map<String, ClassMember> members = new HashMap<String, ClassMember>();
        members.putAll(_members);
        members.putAll(_overrides);
        return members;
    }
    
    public synchronized Map<String, ClassMember> getClassConfigs() {
        if(_actualConfigs == null) {
            _actualConfigs = new HashMap<String, ClassMember>();
            _actualConfigs.putAll(_configs);
            Map<String, AutoDependency> deps = getScopeAutoDependencies();
            for(String name : deps.keySet()) {
                if(_members.containsKey(name)) {
                    ClassMember member = _members.get(name);
                    ClassConfig config = new ClassConfig(this, member.getName(), member.getNode());
                    _actualConfigs.put(name, config);
                }
            }
        }
        return _actualConfigs;
    }
    
    public synchronized Map<String, ClassMember> getConfigs() {
        if(_fullConfigs == null) {
            _fullConfigs = new HashMap<String, ClassMember>();
            for(ClassDefinition base : getAllBaseClasses()) {
                _fullConfigs.putAll(base.getConfigs());
            }
            _fullConfigs.putAll(getClassConfigs());
            // may need to elevate some properties to configs
            // based on parent class configs
            for(Map.Entry<String, ClassMember> entry: _members.entrySet()) {
                String name = entry.getKey();
                if(_fullConfigs.containsKey(name) && !_actualConfigs.containsKey(name)) {
                    ClassMember member = entry.getValue();
                    ClassConfig config = new ClassConfig(this, member.getName(), member.getNode());
                    _actualConfigs.put(name, config);
                    _fullConfigs.put(name, config);
                }
            }
        }
        return _fullConfigs;
    }
    
    public Map<String, ClassMember> getClassMembers() {
        return _members;
    }

    public synchronized Map<String, ClassMember> getMembers() {
        if(_fullMembers == null) {
            _fullMembers = new HashMap<String, ClassMember>();
            
            for(ClassDefinition base : getAllBaseClasses()) {
                _fullMembers.putAll(base.getMembers());
            }
            
            // assign super references here, once we know the set of all base
            // members, and we begin applying members for this class
            for(Map.Entry<String, ClassMember> entry : getClassMembers().entrySet()) {
                String key = entry.getKey();
                ClassMember value = entry.getValue();
                if(_fullMembers.containsKey(key)) {
                    value.setSuperMember(_fullMembers.get(key));
                }
                _fullMembers.put(key, value);
            }
            _fullMembers.putAll(getConfigs());
        }
        return _fullMembers;
    }

    /**
     * Return a list of base and mixin classes in reverse priority order (base class last).
     * used to control map.putAll operations.
     * @return
     */
    public synchronized List<ClassDefinition> getAllBaseClasses() {
        if(_allBases == null) {
            List<ClassDefinition> bases = new ArrayList<ClassDefinition>();
            for(ClassDefinition mixin : CollectionUtil.wrap(getMixedInClasses()).reverse()) {
                bases.add(mixin);
            }
            if(getBaseClass() != null) {
                bases.add(getBaseClass());
            }
            _allBases = bases;
        }
        return _allBases;
    }
    
    public synchronized Set<String> getUnderrides() {
        if(_underrides == null) {
            Set<String> underrides = new HashSet<String>();
            for(ClassConfig config : _configs.values()) {
                config.addGeneratedNames(underrides);
            }
            ClassMember mixConfig = _members.get("mixinConfig");
            if(this.isInstanceOf("Ext.Mixin") && mixConfig != null) {
                BaseNode value = mixConfig.getNode();
                if(value instanceof ObjectLiteral) {
                    Map<String, BaseNode> props = AstUtil.getObjectProperties((ObjectLiteral)value);
                    BaseNode on = props.get("on");
                    if(on != null && on instanceof ObjectLiteral) {
                        Map<String, BaseNode> hooks = AstUtil.getObjectProperties((ObjectLiteral)on);
                        underrides.addAll(hooks.keySet());
                    }
                }
            }
            _underrides = underrides;
        }
        return _underrides;
    }

    public synchronized Set<String> getAllUnderrides() {
        if(_allUnderrides == null) {
            _allUnderrides = new HashSet<String>();
            for(ClassDefinition base : getAllBaseClasses()) {
                _allUnderrides.addAll(base.getAllUnderrides());
            }
            _allUnderrides.addAll(getUnderrides());
        }
        return _allUnderrides;
    }
    
    //-------------------------------------------------------------------------

    private List<String> _classAliases = new ArrayList<String>();
    private Set<String> _classAliasesSet = new HashSet<String>();
    private Set<String> _classXtypesSet = new HashSet<String>();
    private List<String> _classAlternates = new ArrayList<String>();
    private Set<String> _classMixins = new LinkedHashSet<String>();
    private List<String> _classRequires = new ArrayList<String>();
    private List<String> _classUses = new ArrayList<String>();
    private String _processedConfig = "";
    private ObjectLiteral _configLiteral;
    private boolean _isVirtual = false;
    private boolean _isFunctionConfig = false;
    private ClassDefinition _overrideTarget;
    
    //private static final Pattern widgetPre = Pattern.compile("^widget\\.");

    private String _detectedApplicationName;

    private Map<String, AutoDependency> _frameworkAutoDependencies;
    private Map<String, AutoDependency> _detectedAutoDependencies = new LinkedHashMap<String, AutoDependency>();
    private Map<String, AutoDependency> _scopeAutoDependencies = new LinkedHashMap<String, AutoDependency>();

    private String _currentPropertyName;
    private DefineType _type;
    private ClassDefinition _baseClass;
    private Set<ClassDefinition> _mixedInClasses = new LinkedHashSet<ClassDefinition>();
    private String _extendName;
    private String _overrideName;
    private BaseNode _overrideNode;
    private boolean _isSingleton;
    private boolean _isOverride;
    
    private SourceFileSymbols _symbols;
    private Map<String, Boolean> _ignoredNamespaces = new HashMap<String, Boolean>();
    private List<String> _xtypes = new ArrayList<String>();
    private String _baseCls;
    
    private Map<String, ClassMember> _members = new LinkedHashMap<String, ClassMember>();
    private Map<String, ClassMember> _overrides = new LinkedHashMap<String, ClassMember>();
    private Map<String, ClassConfig> _configs = new LinkedHashMap<String, ClassConfig>();

    private List<String> _configNames = new ArrayList<String>(){{
        add("config");
        add("cachedConfig");
        add("renderConfig");
        add("beforeRenderConfig");
    }};
    
    /**
     * fully mapped properties after base class has been set
     */
    private Map<String, ClassMember> _fullMembers = null;
    private Map<String, ClassMember> _fullConfigs = null;
    private Map<String, ClassMember> _actualConfigs = null;
    private List<ClassDefinition> _allBases = null;
    private Set<String> _underrides;
    private Set<String> _allUnderrides;

    private Set<ClassDefinition> _extendedBy = new LinkedHashSet<ClassDefinition>();
    private Set<ClassDefinition> _mixedInBy = new LinkedHashSet<ClassDefinition>();

    private boolean _namedMixins = false;
}
